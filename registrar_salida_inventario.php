<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Obtener los datos enviados
$data = json_decode(file_get_contents('php://input'), true);

// Verificar que se recibieron los datos necesarios
if (!isset($data['dte_id']) || !isset($data['productos'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Datos incompletos']);
    exit;
}

// Si no hay productos con ID, devolver un mensaje informativo
if (empty($data['productos'])) {
    echo json_encode([
        'status' => 'info', 
        'message' => 'No hay productos para registrar salida de inventario',
        'dte_id' => $data['dte_id']
    ]);
    exit;
}

try {
    $conn = getConnection();
    
    // Iniciar transacción
    $conn->beginTransaction();
    
    // Obtener información del DTE para la referencia
    $stmtDTE = $conn->prepare("SELECT tipo_dte, folio FROM tb_facturas_dte WHERE id = :dte_id");
    $stmtDTE->bindParam(':dte_id', $data['dte_id']);
    $stmtDTE->execute();
    $dteInfo = $stmtDTE->fetch(PDO::FETCH_ASSOC);
    
    if (!$dteInfo) {
        throw new Exception("No se encontró el DTE con ID: " . $data['dte_id']);
    }
    
    // Crear la referencia para el movimiento
    $tipoDocumento = '';
    switch ($dteInfo['tipo_dte']) {
        case '33': $tipoDocumento = 'Factura'; break;
        case '34': $tipoDocumento = 'Factura Exenta'; break;
        case '39': $tipoDocumento = 'Boleta'; break;
        case '61': $tipoDocumento = 'Nota de Crédito'; break;
        default: $tipoDocumento = 'DTE'; break;
    }
    $referencia = $tipoDocumento . ' #' . $dteInfo['folio'];
    
    // Preparar la consulta para insertar movimientos de inventario
    $stmt = $conn->prepare("INSERT INTO movimiento_inventario (
        repuesto_id, 
        tipo_movimiento, 
        cantidad, 
        fecha_movimiento, 
        usuario_id, 
        referencia,
        almacen_id,
        lote
    ) VALUES (
        :repuesto_id, 
        'SALIDA', 
        :cantidad, 
        NOW(), 
        :usuario_id, 
        :referencia,
        :almacen_id,
        :lote
    )");
    
    // Contador de productos procesados
    $productosRegistrados = 0;
    $productosOmitidos = 0;
    $errores = [];
    
    // Obtener el ID del usuario (si está disponible)
    $usuarioId = isset($data['usuario_id']) ? $data['usuario_id'] : 1; // Valor por defecto si no se proporciona
    
    // Obtener el ID del almacén (si está disponible)
    $almacenId = isset($data['almacen_id']) ? $data['almacen_id'] : 1; // Valor por defecto si no se proporciona
    
    // Insertar cada movimiento de inventario
    foreach ($data['productos'] as $producto) {
        // Verificar que el producto tenga un ID válido
        if (empty($producto['repuesto_id'])) {
            $productosOmitidos++;
            continue; // Saltar este producto si no tiene ID
        }
        
        // Verificar que la cantidad sea válida
        $cantidad = intval($producto['cantidad']);
        if ($cantidad <= 0) {
            $productosOmitidos++;
            continue; // Saltar este producto si la cantidad no es válida
        }
        
        // Verificar si hay stock suficiente
        $stmtStock = $conn->prepare("SELECT SUM(cantidad) as stock_total FROM stock WHERE repuesto_id = :repuesto_id AND almacen_id = :almacen_id");
        $stmtStock->bindParam(':repuesto_id', $producto['repuesto_id']);
        $stmtStock->bindParam(':almacen_id', $almacenId);
        $stmtStock->execute();
        $stockInfo = $stmtStock->fetch(PDO::FETCH_ASSOC);
        
        $stockDisponible = $stockInfo ? intval($stockInfo['stock_total']) : 0;
        
        if ($stockDisponible < $cantidad) {
            // Stock insuficiente, registrar el error pero continuar con otros productos
            $errores[] = [
                'repuesto_id' => $producto['repuesto_id'],
                'nombre' => $producto['nombre_producto'] ?? 'Producto ID: ' . $producto['repuesto_id'],
                'cantidad_solicitada' => $cantidad,
                'stock_disponible' => $stockDisponible
            ];
            $productosOmitidos++;
            continue;
        }
        
        // Obtener el lote más antiguo con stock disponible (FIFO)
        $stmtLotes = $conn->prepare("
            SELECT lote, cantidad 
            FROM stock 
            WHERE repuesto_id = :repuesto_id 
            AND almacen_id = :almacen_id 
            AND cantidad > 0 
            ORDER BY fecha_ingreso ASC
        ");
        $stmtLotes->bindParam(':repuesto_id', $producto['repuesto_id']);
        $stmtLotes->bindParam(':almacen_id', $almacenId);
        $stmtLotes->execute();
        $lotes = $stmtLotes->fetchAll(PDO::FETCH_ASSOC);
        
        $cantidadPendiente = $cantidad;
        
        foreach ($lotes as $lote) {
            $cantidadLote = min($cantidadPendiente, intval($lote['cantidad']));
            
            if ($cantidadLote <= 0) continue;
            
            // Registrar el movimiento de salida para este lote
            $stmt->bindParam(':repuesto_id', $producto['repuesto_id']);
            $stmt->bindParam(':cantidad', $cantidadLote);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->bindParam(':referencia', $referencia);
            $stmt->bindParam(':almacen_id', $almacenId);
            $stmt->bindParam(':lote', $lote['lote']);
            
            $stmt->execute();
            
            $cantidadPendiente -= $cantidadLote;
            
            if ($cantidadPendiente <= 0) break;
        }
        
        $productosRegistrados++;
    }
    
    // Confirmar la transacción
    $conn->commit();
    
    // Preparar mensaje según los resultados
    $mensaje = "Se registraron salidas de inventario para $productosRegistrados productos";
    
    if ($productosOmitidos > 0) {
        $mensaje .= ". Se omitieron $productosOmitidos productos (sin ID o cantidad inválida)";
    }
    
    if (!empty($errores)) {
        $mensaje .= ". Algunos productos no tenían stock suficiente";
    }
    
    echo json_encode([
        'status' => 'success', 
        'message' => $mensaje,
        'dte_id' => $data['dte_id'],
        'productos_registrados' => $productosRegistrados,
        'productos_omitidos' => $productosOmitidos,
        'errores_stock' => $errores
    ]);
    
} catch (Exception $e) {
    // Revertir la transacción en caso de error
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => 'Error al registrar salidas de inventario: ' . $e->getMessage()
    ]);
}
?>
