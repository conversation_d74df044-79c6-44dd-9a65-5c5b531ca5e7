<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    $conn = getConnection();
    $query = "SELECT distinct 
        vc.id,
        vc.modelo_id,
        vc.anio_inicio,
        vc.anio_fin,
        vc.cilindrada,
        vc.version,
        vc.tipo_motor,
        vc.combustible,
        m.nombre as modelo_nombre,
        ma.id as marca_id,
        ma.nombre as marca_nombre
    FROM vehiculo_compatible vc
    JOIN modelo m ON vc.modelo_id = m.id
    JOIN marca ma ON m.marca_id = ma.id
    ORDER BY ma.nombre, m.nombre";
    
    $stmt = $conn->query($query);
    echo json_encode($stmt->fetchAll(PDO::FETCH_ASSOC));
    
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
