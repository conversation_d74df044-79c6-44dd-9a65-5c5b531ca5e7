/* Variables globales de colores */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #e74c3c;
    --accent-color: #3498db;
    --hover-color: #e67e22;
}

/* Estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Estilos del header principal */
header {
    background: linear-gradient(135deg, var(--primary-color), #34495e);
    padding: 0.5rem !important; /* Reducido de 1rem */
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 0.5rem; /* Reducido de 1rem */
}

/* Estilos del logo */
.logo {
    color: white;
    font-size: 1.2rem; /* Reducido de 1.8rem */
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 0.3rem; /* Reducido de 0.5rem */
}

.logo i {
    color: var(--secondary-color);
}

/* Estilos de los módulos */
.modules-menu {
    display: flex;
    gap: 0.5rem; /* Reducido de 1rem */
    list-style: none;
    margin: 0;
    padding: 0;
}

.modules-menu li {
    padding: 0.4rem 0.8rem; /* Reducido de 0.8rem 1.5rem */
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px; /* Reducido de 6px */
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem; /* Añadido para reducir tamaño de texto */
}

.modules-menu li a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.modules-menu li.active,
.modules-menu li:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
}

.modules-menu li i {
    font-size: 0.9rem; /* Reducido el tamaño de los iconos */
}

.modules-menu li span {
    margin-left: 0.3rem; /* Añadido para reducir espacio entre icono y texto */
}

/* Estilos de los iconos y dropdown */
.icon-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    z-index: 10000;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.4rem;
    color: white;
}

.cart-icon i {
    position: relative;
    z-index: 1;
}

.cart-count {
    position: absolute;
    top: -10px;
    right: 109px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.user-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.4rem;
    color: white;
    transition: color 0.3s ease;
}

.user-icon:hover {
    color: var(--hover-color);
}

/* Dropdown is controlled by JS */
.user-dropdown {
    display: none;
    position: absolute;
    top: calc(100% + 5px);
    right: -10px;
    margin-top: 0.5rem;
    z-index: 10001;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    min-width: 120px;
    overflow: hidden;
}

@keyframes dropdownFade {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/*  The following javascript code will need to be added to handle the click event and the active class */
/*  Example:
document.querySelector('.user-icon').addEventListener('click', function(){
    document.querySelector('.user-dropdown').classList.toggle('active');
});
*/

.user-dropdown a {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.2s ease;
    gap: 6px;
    font-weight: 500;
    font-size: 0.85rem;
}

.user-dropdown a:hover {
    background-color: #f5f5f5;
    color: var(--secondary-color);
}

/* Change hover to click behavior */
.user-dropdown.active {
    display: block;
    animation: dropdownFade 0.2s ease-out;
}

/* Estilo específico para el botón de cerrar sesión */
.logout-btn {
    border-top: 1px solid #f0f0f0;
    margin-top: 2px;
}

.logout-btn i {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Responsive styles */
@media (max-width: 1024px) {
    .modules-menu span {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: var(--primary-color);
        padding: 0.5rem;
        z-index: 1001;
        height: auto;
    }

    .modules-menu {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: space-around;
        padding: 0.5rem;
        background: var(--primary-color);
        margin: 0;
        z-index: 1001;
    }

    .modules-menu li {
        padding: 0.5rem;
        background: none;
    }

    .modules-menu li span {
        display: none;
    }

    .modules-menu li i {
        font-size: 1.4rem;
    }

    body {
        padding-top: 70px;
        padding-bottom: 70px;
    }
}
