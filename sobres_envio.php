<?php
// Incluir verificación de autenticación
require_once 'auth_check.php';

// Configuración adicional de caché
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado

require_once 'config.php';
?>
<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Gestión de Sobres de Envío - TATA REPUESTOS</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">

    <!-- CSS externos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- CSS internos -->
    <link rel="stylesheet" href="styles/table.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/header.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/inventory.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/controls.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/index.css?v=<?php echo time(); ?>">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="diagnostic_tool.js?v=<?php echo time(); ?>"></script>
</head>

<body>
    <!-- Header principal con logo y navegación -->
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-cogs"></i>
                Tata repuestos
            </div>
            <ul class="modules-menu">
                <li><a href="index.php" class="module-link"><i class="fas fa-cash-register"></i><span>Módulo Venta</span></a></li>
                <li><a href="inventory.php" class="module-link"><i class="fas fa-boxes"></i><span>Módulo Inventario</span></a></li>
                <li><a href="ventas.php" class="module-link"><i class="fas fa-chart-bar"></i><span>Módulo Reportería</span></a></li>
                <li><a href="sobres_envio.php" class="module-link"><i class="fas fa-envelope"></i><span>Módulo de Sobres</span></a></li>
                <li><a href="mod_config.php" class="module-link"><i class="fas fa-cog"></i><span>Módulo Configuración</span></a></li>
            </ul>
            <div class="cart-icon">
                <div class="icon-container">
                    <div class="user-dropdown">
                        <a href="login.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Contenido principal -->
    <div class="container" style="margin: 20px; padding: 20px; background: #fff; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h1 class="page-title">
            <i class="fas fa-envelope"></i> Gestión de Sobres de Envío
        </h1>

        <div class="actions-menu">
            <!-- Grupo de botones principales -->
            <div class="button-group primary-actions">
                <button type="button" id="generateEnvelopeBtn" class="json-btn" style="background-color: #8e44ad;">
                    <i class="fas fa-plus"></i> Generar Sobre
                </button>

                <button type="button" id="refreshBtn" class="json-btn" style="background-color: #8e44ad;">
                    <i class="fas fa-sync-alt"></i> Actualizar
                </button>

                <button type="button" id="solicitarFoliosBtn" class="json-btn" style="background-color: #2980b9;">
                    <i class="fas fa-file-invoice"></i> Solicitar Folios
                </button>

                <button type="button" id="diagnosticoBtn" class="json-btn" style="background-color: #e67e22;">
                <i class="fas fa-stethoscope"></i> Diagnosticar
                </button>

            <a href="error_dashboard.php" class="json-btn" style="background-color: #c0392b;">
                <i class="fas fa-exclamation-triangle"></i> Ver Errores
            </a>
            </div>

            <button type="button" id="enviarSobreBtn" class="json-btn" style="background-color: #8e44ad;">
                <i class="fas fa-envelope"></i> Enviar Sobre DTE
            </button>

            <!-- Grupo de botones de procesamiento con dropdown -->
            <div class="button-group processing-actions">
                <div class="dte-type-dropdown">
                    <button type="button" id="processPendingBtn" class="json-btn" style="background-color: #8e44ad;">
                        <i class="fas fa-play"></i> Procesar Pendientes
                    </button>
                    <div class="dropdown-container">
                        <button type="button" onclick="toggleDteTypeMenu()" class="json-btn" style="background-color: #8e44ad;">
                            <i class="fas fa-caret-down"></i>
                        </button>
                        <div id="dteTypeMenu" class="dropdown-menu">
                            <button class="dropdown-item" onclick="processPendingDTEs('all')">Todos los tipos</button>
                            <button class="dropdown-item" onclick="processPendingDTEs('33')">Solo Facturas (33)</button>
                            <button class="dropdown-item" onclick="processPendingDTEs('39')">Solo Boletas (39)</button>
                            <button class="dropdown-item" onclick="processPendingDTEs('56')">Solo Notas Débito (56)</button>
                            <button class="dropdown-item" onclick="processPendingDTEs('61')">Solo Notas Crédito (61)</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h3 class="mb-4">
                <i class="fas fa-file-invoice me-2"></i> DTEs pendientes de envío
            </h3>

            <div id="pendingSummary" class="alert alert-info">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                Cargando información...
            </div>

            <div class="table-responsive">
                <table class="table table-hover align-middle" id="dteSummaryTable" style="display: none;">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-nowrap">
                                <i class="fas fa-file-alt me-2"></i>Tipo DTE
                            </th>
                            <th scope="col">Descripción</th>
                            <th scope="col" class="text-center">Cantidad</th>
                            <th scope="col" class="text-center">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Los datos se cargarán dinámicamente -->
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <td colspan="2" class="fw-bold">Total pendientes:</td>
                            <td id="totalPendingCount" class="text-center fw-bold">0</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <div id="loader" class="loader">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Procesando solicitud...</p>
        </div>

        <div id="responseMessage" style="margin-bottom: 20px; padding: 10px; border-radius: 5px; display: none;"></div>

        <div id="detailedLogsContainer" class="detailed-logs">
            <h3><i class="fas fa-terminal"></i> Logs del Proceso</h3>
            <div class="log-controls">
                <button id="clearLogsBtn" class="action-btn">
                    <i class="fas fa-trash"></i> Limpiar Logs
                </button>
                <button id="toggleAutoScrollBtn" class="action-btn">
                    <i class="fas fa-scroll"></i> Auto-scroll
                </button>
            </div>
            <div id="logContent" class="log-content"></div>
        </div>

        <h2><i class="fas fa-list"></i> Registro de Sobres de Envío</h2>

        <div class="table-container mt-4">
            <div class="card shadow">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table id="envelopesTable" class="table table-hover align-middle mb-0">
                            <thead>
                                <tr class="bg-light">
                                    <th class="px-4" style="width: 80px;">ID</th>
                                    <th>Nombre Archivo</th>
                                    <th style="width: 150px;">Fecha</th>
                                    <th style="width: 120px;">Estado</th>
                                    <th style="width: 100px;">Tipo</th>
                                    <th style="width: 120px;">Track ID</th>
                                    <th>Emisor</th>
                                    <th>Receptor</th>
                                    <th class="text-center" style="width: 100px;">Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Los datos se cargarán mediante AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <style>
        /* Estilos para la tabla de sobres */
        .table-container {
            margin: 20px 0;
        }

        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
        }

        #envelopesTable {
            font-size: 14px;
        }

        #envelopesTable thead tr {
            background-color: #f8f9fa;
        }

        #envelopesTable th {
            font-weight: 600;
            color: #495057;
            border-bottom: none;
            padding: 15px;
            white-space: nowrap;
        }

        #envelopesTable td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            color: #444;
        }

        #envelopesTable tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* Estilos para los estados */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-generado {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status-procesado {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        /* Estilos para los botones de acción */
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .action-btn {
            padding: 6px;
            border: none;
            background: transparent;
            color: #6c757d;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .action-btn:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        /* Estilos para el tipo de documento */
        .doc-type {
            font-size: 13px;
            color: #6c757d;
        }

        /* Estilos responsivos */
        @media (max-width: 768px) {
            #envelopesTable {
                font-size: 13px;
            }

            #envelopesTable td,
            #envelopesTable th {
                padding: 10px;
            }
        }
        </style>

        <div id="logContainer" class="log-container" style="display: none;">
            <div class="log-header">
                <h3>Logs del Proceso</h3>
                <button onclick="clearLogs()" class="clear-logs-btn">
                    <i class="fas fa-trash"></i> Limpiar
                </button>
            </div>
            <div id="logContent"></div>
        </div>
    </div>

    <style>
        .detailed-logs {
            margin: 20px 0;
            padding: 15px;
            background: #1e1e1e;
            border-radius: 5px;
            color: #fff;
        }

        .log-controls {
            margin-bottom: 10px;
            display: flex;
            gap: 10px;
        }

        .log-content {
            height: 400px;
            overflow-y: auto;
            background: #000;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.4;
            border-radius: 3px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid;
        }

        .log-entry.info { border-color: #2196F3; color: #89CFF0; }
        .log-entry.success { border-color: #4CAF50; color: #98FB98; }
        .log-entry.error { border-color: #F44336; color: #FF6B6B; }
        .log-entry.warning { border-color: #FFC107; color: #FFD700; }
        .log-entry.debug { border-color: #9C27B0; color: #DDA0DD; }

        .log-entry .timestamp {
            color: #666;
            margin-right: 10px;
        }

        .log-entry .details {
            margin-top: 5px;
            padding-left: 20px;
            color: #888;
        }

        .log-entry pre {
            margin: 5px 0;
            padding: 5px;
            background: #2a2a2a;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Función especializada para formatear detalles de errores en los logs
        function formatErrorDetails(errorData) {
            // Si es un objeto simple, mostrarlo como JSON
            if (typeof errorData !== 'object' || errorData === null) {
                return `<pre>${JSON.stringify(errorData, null, 2)}</pre>`;
            }

            let html = '<div style="margin: 10px 0; padding: 10px; background: rgba(244,67,54,0.05); border-radius: 4px;">';

            // Primero mostramos mensajes y códigos importantes
            if (errorData.message) {
                html += `<div style="font-weight: bold; margin-bottom: 5px;">Mensaje: ${errorData.message}</div>`;
            }

            // Mostrar código HTTP si está disponible
            if (errorData.http_code) {
                const codeColor = errorData.http_code >= 400 ? '#f44336' : '#4caf50';
                html += `<div style="margin-top: 5px; font-weight: bold; color: ${codeColor};">
                    Código HTTP: ${errorData.http_code}
                </div>`;

                // Agregar explicación para códigos HTTP comunes
                if (errorData.http_code === 404) {
                    html += `<div style="margin-top: 5px; padding: 8px; background: rgba(244,67,54,0.1); border-left: 3px solid #f44336;">
                        <strong>Error 404:</strong> No se encontró el recurso solicitado. Esto puede indicar que los archivos XML no existen en las rutas especificadas.
                    </div>`;
                } else if (errorData.http_code === 500) {
                    html += `<div style="margin-top: 5px; padding: 8px; background: rgba(244,67,54,0.1); border-left: 3px solid #f44336;">
                        <strong>Error 500:</strong> Error interno del servidor. Revise los logs del servidor para más detalles.
                    </div>`;
                } else if (errorData.http_code === 400) {
                    html += `<div style="margin-top: 5px; padding: 8px; background: rgba(244,67,54,0.1); border-left: 3px solid #f44336;">
                        <strong>Error 400:</strong> Solicitud incorrecta. Verifique los parámetros enviados.
                    </div>`;
                }
            } else if (errorData.code) {
                html += `<div>Código: ${errorData.code}</div>`;
            }

            // Si hay un detalle de error específico, mostrarlo destacado
            if (errorData.detalle_error) {
                html += `<div style="margin-top: 8px; padding: 8px; background: rgba(244,67,54,0.1); border-left: 3px solid #f44336; font-weight: bold;">
                    Detalle del error: ${errorData.detalle_error}
                </div>`;
            }

            // Si hay un error específico, mostrarlo
            if (errorData.error) {
                html += `<div style="margin-top: 8px; padding: 8px; background: rgba(244,67,54,0.1); border-left: 3px solid #f44336;">
                    <strong>Error:</strong> ${errorData.error}
                </div>`;
            }

            // Si hay tipos de DTE encontrados, mostrarlos
            if (errorData.tipos_encontrados) {
                html += '<div style="margin-top: 8px; padding: 8px; background: rgba(33,150,243,0.1); border-left: 3px solid #2196f3;">';
                html += '<strong>Tipos de DTE encontrados:</strong> ';

                if (Array.isArray(errorData.tipos_encontrados)) {
                    html += errorData.tipos_encontrados.join(', ');
                } else {
                    html += JSON.stringify(errorData.tipos_encontrados);
                }

                html += '</div>';
            }

            // Si hay conteo por tipo, mostrarlo
            if (errorData.conteo_por_tipo) {
                html += '<div style="margin-top: 8px; padding: 8px; background: rgba(33,150,243,0.1); border-left: 3px solid #2196f3;">';
                html += '<strong>Conteo por tipo de DTE:</strong><br>';

                for (const tipo in errorData.conteo_por_tipo) {
                    html += `<span style="display: inline-block; margin: 2px; padding: 2px 6px; background: #2196f3; color: white; border-radius: 12px; font-size: 0.85em;">
                        Tipo ${tipo}: ${errorData.conteo_por_tipo[tipo]} documento(s)
                    </span> `;
                }

                html += '</div>';
            }

            // Si hay categorías de error, mostrarlas de forma destacada
            if (errorData.error_categorias && errorData.error_categorias.length > 0) {
                html += '<div style="margin-top: 8px;">Posibles causas: ';
                errorData.error_categorias.forEach(cat => {
                    const catColor = {
                        'file_not_found': '#e91e63',
                        'permission_denied': '#f44336',
                        'memory_limit': '#ff9800',
                        'timeout': '#ff5722',
                        'database_error': '#673ab7',
                        'api_error': '#2196f3',
                        'xml_error': '#009688',
                        'multiple_dte_types': '#ff5722'
                    }[cat] || '#757575';

                    html += `<span style="display: inline-block; margin: 2px; padding: 2px 6px; background: ${catColor}; color: white; border-radius: 12px; font-size: 0.85em;">${formatErrorCategory(cat)}</span>`;
                });
                html += '</div>';
            }

            // Si hay una categoría de error específica
            if (errorData.error_categoria) {
                const catColor = {
                    'file_not_found': '#e91e63',
                    'permission_denied': '#f44336',
                    'memory_limit': '#ff9800',
                    'timeout': '#ff5722',
                    'database_error': '#673ab7',
                    'api_error': '#2196f3',
                    'xml_error': '#009688',
                    'multiple_dte_types': '#ff5722'
                }[errorData.error_categoria] || '#757575';

                html += `<div style="margin-top: 8px;">Categoría de error:
                    <span style="display: inline-block; margin: 2px; padding: 2px 6px; background: ${catColor}; color: white; border-radius: 12px; font-size: 0.85em;">
                        ${formatErrorCategory(errorData.error_categoria)}
                    </span>
                </div>`;
            }

            // Información sobre archivos y líneas de error
            if (errorData.file) {
                html += `<div style="margin-top: 5px;">Archivo: ${errorData.file} (línea ${errorData.line || 'desconocida'})</div>`;
            }

            // Si es un diagnóstico completo, mostrar secciones especiales
            if (errorData.exception_info) {
                html += formatExceptionInfo(errorData.exception_info);
            }

            // Información sobre el contexto
            if (errorData.context_info || errorData.context) {
                const contextInfo = errorData.context_info || errorData.context;
                html += '<div style="margin-top: 10px;">';
                html += '<details>';
                html += '<summary style="cursor: pointer; color: #1976d2;">Información de contexto</summary>';
                html += `<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">${JSON.stringify(contextInfo, null, 2)}</pre>`;
                html += '</details>';
                html += '</div>';
            }

            // Información detallada sobre diagnóstico
            if (errorData.diagnostico) {
                html += '<div style="margin-top: 10px;">';
                html += '<details open>'; // Abierto por defecto para mostrar el diagnóstico
                html += '<summary style="cursor: pointer; color: #1976d2;">Diagnóstico detallado</summary>';

                // Si hay información de archivos en el diagnóstico, mostrarla de forma más amigable
                if (errorData.diagnostico.info_archivos) {
                    html += '<div style="margin-top: 8px; padding: 8px; background: rgba(0,0,0,0.02); border-radius: 4px;">';
                    html += '<strong>Información de archivos:</strong>';
                    html += '<table style="width: 100%; border-collapse: collapse; margin-top: 8px;">';
                    html += '<tr style="background: rgba(0,0,0,0.05);">';
                    html += '<th style="padding: 5px; text-align: left; border: 1px solid #ddd;">ID</th>';
                    html += '<th style="padding: 5px; text-align: left; border: 1px solid #ddd;">Tipo DTE</th>';
                    html += '<th style="padding: 5px; text-align: left; border: 1px solid #ddd;">Archivo</th>';
                    html += '<th style="padding: 5px; text-align: left; border: 1px solid #ddd;">Existe</th>';
                    html += '<th style="padding: 5px; text-align: left; border: 1px solid #ddd;">Tamaño</th>';
                    html += '</tr>';

                    errorData.diagnostico.info_archivos.forEach(archivo => {
                        const rowColor = archivo.existe === 'sí' ? '' : 'background: rgba(244,67,54,0.05);';
                        html += `<tr style="${rowColor}">`;
                        html += `<td style="padding: 5px; border: 1px solid #ddd;">${archivo.id}</td>`;
                        html += `<td style="padding: 5px; border: 1px solid #ddd;">${archivo.tipo_dte}</td>`;
                        html += `<td style="padding: 5px; border: 1px solid #ddd;">${archivo.ruta.split('/').pop()}</td>`;
                        html += `<td style="padding: 5px; border: 1px solid #ddd; color: ${archivo.existe === 'sí' ? 'green' : 'red'};">${archivo.existe}</td>`;
                        html += `<td style="padding: 5px; border: 1px solid #ddd;">${archivo.tamaño}</td>`;
                        html += '</tr>';
                    });

                    html += '</table>';
                    html += '</div>';
                } else {
                    html += `<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">${JSON.stringify(errorData.diagnostico, null, 2)}</pre>`;
                }

                html += '</details>';
                html += '</div>';
            }

            // Si hay traza, mostrarla en modo colapsable
            if (errorData.trace) {
                html += '<div style="margin-top: 10px;">';
                html += '<details>';
                html += '<summary style="cursor: pointer; color: #1976d2;">Traza de error</summary>';
                html += '<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">';
                if (Array.isArray(errorData.trace)) {
                    html += errorData.trace.join('\n');
                } else {
                    html += errorData.trace;
                }
                html += '</pre>';
                html += '</details>';
                html += '</div>';
            }

            // Resto de información en formato JSON colapsable
            const otherKeys = Object.keys(errorData).filter(k =>
                !['message', 'code', 'file', 'line', 'error_categorias', 'exception_info',
                  'context_info', 'context', 'diagnostico', 'trace', 'detalle_error', 'tipos_encontrados'].includes(k));

            if (otherKeys.length > 0) {
                html += '<div style="margin-top: 10px;">';
                html += '<details>';
                html += '<summary style="cursor: pointer; color: #1976d2;">Información adicional</summary>';
                html += '<pre style="margin: 5px 0; padding: 5px; background: rgba(0,0,0,0.03); max-height: 150px; overflow: auto;">';
                const additionalInfo = {};
                otherKeys.forEach(key => {
                    additionalInfo[key] = errorData[key];
                });
                html += JSON.stringify(additionalInfo, null, 2);
                html += '</pre>';
                html += '</details>';
                html += '</div>';
            }

            html += '</div>';
            return html;
        }

        // Formatear categorías de error para mejor legibilidad
        function formatErrorCategory(category) {
            const categoryLabels = {
                'file_not_found': 'Archivo no encontrado',
                'permission_denied': 'Permiso denegado',
                'memory_limit': 'Límite de memoria',
                'timeout': 'Tiempo de espera agotado',
                'database_error': 'Error de base de datos',
                'api_error': 'Error de API',
                'xml_error': 'Error de XML',
                'multiple_dte_types': 'Múltiples tipos de DTE'
            };

            return categoryLabels[category] || category;
        }

        // Formatear información de excepción
        function formatExceptionInfo(exceptionInfo) {
            let html = '<div style="margin-top: 8px; padding: 8px; background: rgba(0,0,0,0.02); border-left: 3px solid #f44336;">';
            html += `<div style="font-weight: bold;">Excepción: ${exceptionInfo.class || 'Desconocida'}</div>`;
            if (exceptionInfo.file) {
                html += `<div>Archivo: ${exceptionInfo.file} (línea ${exceptionInfo.line || 'desconocida'})</div>`;
            }
            html += '</div>';
            return html;
        }
        $(document).ready(function() {
            // Cargar datos iniciales
            loadData();

            // Configurar botones
            $("#refreshBtn").click(function() {
                loadData();
            });

            $("#generateEnvelopeBtn").click(function() {
                generateEnvelope();
            });

            $("#processPendingBtn").click(function() {
                processPendingDTEs('all');
            });

            // Botón para solicitar folios
            $("#solicitarFoliosBtn").click(function() {
                solicitarFolios();
            });

            // Botón para herramienta de diagnóstico
            $("#diagnosticoBtn").click(function() {
                runDiagnosticTool();
            });

            // Ocultar menú de tipos DTE cuando se hace clic fuera
            window.onclick = function(event) {
                if (!event.target.matches('.dte-type-btn') && !event.target.matches('.fa-caret-down')) {
                    var dropdowns = document.getElementsByClassName("dte-type-menu");
                    for (var i = 0; i < dropdowns.length; i++) {
                        var openDropdown = dropdowns[i];
                        if (openDropdown.classList.contains('show')) {
                            openDropdown.classList.remove('show');
                        }
                    }
                }
            }
        });

        function toggleDteTypeMenu() {
            document.getElementById("dteTypeMenu").classList.toggle("show");
        }

        function loadData() {
            // Ocultar mensajes previos
            $("#responseMessage").hide();

            // Cargar sobres
            $.ajax({
                url: 'api_sobres.php?action=list',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.error) {
                        showMessage('error', data.error);
                        return;
                    }

                    updateTable(data.sobres);
                    $("#envelopeCount").text(data.total_sobres);
                },
                error: function(xhr, status, error) {
                    console.error("Error en la solicitud AJAX:", status, error);
                    console.log("Respuesta:", xhr.responseText);
                    showMessage('error', 'Error al cargar los datos: ' + error);
                }
            });

            // Cargar conteo detallado de pendientes por tipo
            loadPendingDocumentsDetail();
        }

        function loadPendingDocumentsDetail() {
            $.ajax({
                url: 'api_sobres.php?action=pending_detail',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.error) {
                        $("#pendingSummary").text("Error al cargar detalle: " + data.error);
                        return;
                    }

                    updatePendingSummary(data.pending_by_type);
                },
                error: function() {
                    $("#pendingSummary").text("Error al conectar con el servidor");
                }
            });
        }

        function updatePendingSummary(pendingByType) {
            const summaryTable = $("#dteSummaryTable");
            const tbody = summaryTable.find("tbody");
            tbody.empty();

            // Mapeo de tipos de DTE a descripciones
            const dteDescriptions = {
                '33': 'Factura Electrónica',
                '39': 'Boleta Electrónica',
                '56': 'Nota de Débito',
                '61': 'Nota de Crédito'
            };

            let totalPending = 0;

            if (pendingByType.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="4" style="text-align: center;">No hay documentos pendientes</td>
                    </tr>
                `);
            } else {
                pendingByType.forEach(item => {
                    const tipoDTE = item.tipo_dte;
                    const cantidad = parseInt(item.cantidad);
                    totalPending += cantidad;

                    tbody.append(`
                        <tr>
                            <td><span class="dte-type-badge dte-type-${tipoDTE || 'default'}">${tipoDTE || 'N/A'}</span></td>
                            <td>${dteDescriptions[tipoDTE] || 'Tipo desconocido'}</td>
                            <td>${cantidad}</td>
                            <td>
                                <button class="process-btn" onclick="processPendingDTEs('${tipoDTE}')">
                                    <i class="fas fa-play"></i> Procesar
                                </button>
                            </td>
                        </tr>
                    `);
                });
            }

            $("#totalPendingCount").text(totalPending);
            summaryTable.show();
        }

        function getStatusText(statusCode) {
            switch(parseInt(statusCode)) {
                case 0: return 'generado';
                case 1: return 'procesado';
                case 2: return 'aceptado';
                case 3: return 'rechazado';
                case 4: return 'error';
                default: return 'desconocido';
            }
        }

        function getStatusClass(statusCode) {
            return 'status-' + getStatusText(statusCode);
        }

        function updateTable(sobres) {
            const tbody = $("#envelopesTable tbody");
            tbody.empty();

            if (sobres.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="9" style="text-align: center;">No hay sobres registrados</td>
                    </tr>
                `);
                return;
            }

            sobres.forEach(sobre => {
                const statusText = getStatusText(sobre.estado_envio);
                const statusClass = getStatusClass(sobre.estado_envio);

                tbody.append(`
                    <tr>
                        <td>${sobre.id}</td>
                        <td title="${sobre.nombre_archivo}">${getFileName(sobre.nombre_archivo)}</td>
                        <td>${formatDate(sobre.fecha)}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td>${sobre.tipoEnvio || 'N/A'}</td>
                        <td>${sobre.trackid || 'N/A'}</td>
                        <td>${sobre.rut_emisor || 'N/A'}</td>
                        <td>${sobre.rut_receptor || 'N/A'}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <button class="btn btn-info btn-sm me-2" onclick="viewEnvelope(${sobre.id})"
                                        data-bs-toggle="tooltip" title="Ver detalles">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-success btn-sm" onclick="downloadFile('${sobre.nombre_archivo}')"
                                        data-bs-toggle="tooltip" title="Descargar archivo">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `);
            });
        }

        function getFileName(path) {
            if (!path) return 'N/A';
            return path.split('/').pop();
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        // Variable para controlar el estado del botón
        let sobreGenerationLocked = false;

        function generateEnvelope() {
            // Verificar si ya hay un proceso en curso
            if (processingInProgress) {
                showMessage('warning', 'Ya hay un proceso en curso. Por favor espere.');
                return;
            }

            // Verificar si el botón está bloqueado (prevención de doble clic)
            if (sobreGenerationLocked) {
                showMessage('warning', 'Solicitud en proceso. Por favor espere unos segundos antes de intentar nuevamente.');
                return;
            }

            // Bloquear el botón para evitar múltiples solicitudes
            sobreGenerationLocked = true;
            $("#generateEnvelopeBtn").prop('disabled', true).css('opacity', '0.6');

            // Iniciar el proceso
            processingInProgress = true;
            $("#loader").show();
            $("#responseMessage").hide();

            // Limpiar logs anteriores
            clearLogs();

            // Mostrar el contenedor de logs (asegurarse de que ambos contenedores estén visibles)
            $("#logContainer").show();
            $("#detailedLogsContainer").show();

            // Agregar entrada de log inicial
            addLogEntry('info', 'Iniciando proceso de generación de sobre');
            addLogEntry('info', 'Verificando permisos de directorios');

            // Generar un token único para esta solicitud
            const requestToken = Date.now().toString() + Math.random().toString(36).substring(2, 15);

            // Primero verificar permisos
            $.ajax({
                url: 'check_permissions.php',
                type: 'GET',
                dataType: 'json',
                success: function(permData) {
                    if (permData.success) {
                        addLogEntry('success', 'Permisos de directorios verificados', permData.directories);
                        proceedWithEnvelope(requestToken);
                    } else {
                        addLogEntry('error', 'Problemas con permisos de directorios', permData.directories);
                        showMessage('warning', 'Posibles problemas de permisos detectados. Puede continuar pero podría fallar.');
                        proceedWithEnvelope(requestToken);
                    }
                },
                error: function() {
                    addLogEntry('warning', 'No se pudieron verificar los permisos, continuando de todos modos');
                    proceedWithEnvelope(requestToken);
                }
            });

            function proceedWithEnvelope(requestToken) {
                // Verificar si hay un tipo seleccionado en la tabla de resumen
                let selectedType = null;
                if ($("#dteSummaryTable tbody tr").length === 1) {
                    // Si solo hay un tipo de DTE pendiente, usamos ese
                    const tipoBadge = $("#dteSummaryTable tbody tr:first-child .dte-type-badge");
                    if (tipoBadge.length) {
                        selectedType = tipoBadge.text();
                    }
                }

                // Mostrar tipo de DTE en logs
                if (selectedType) {
                    addLogEntry('info', `Generando sobre para documentos de tipo ${selectedType}`);
                } else {
                    addLogEntry('info', 'Generando sobre sin tipo específico');
                }

                const ajaxData = {
                    request_token: requestToken, // Incluir el token único en la solicitud
                    limite: 50 // Establecer el límite a 50 documentos por sobre
                };

                if (selectedType) {
                    ajaxData.tipo_dte = selectedType;
                }

                $.ajax({
                    url: 'generar_sobre.php',
                    type: 'POST',
                    dataType: 'json',
                    data: ajaxData,
                    success: function(data) {
                        $("#loader").hide();

                        if (data.logs) {
                            data.logs.forEach(log => {
                                addLogEntry(log.type, log.message, log.data);
                            });
                        }

                        if (data.error) {
                            addLogEntry('error', 'Error en el proceso', data.details || data.error);

                            // Mostrar mensaje de error con detalles si existen
                            if (data.details) {
                                showMessage('error', data.error, data.details);
                            } else if (data.response_details) {
                                showMessage('error', data.error, data.response_details);
                            } else {
                                showMessage('error', data.error);
                            }
                        } else {
                            addLogEntry('success', 'Sobre generado exitosamente', {
                                sobre_id: data.sobre_id,
                                documentos: data.documentos_incluidos,
                                archivo: data.archivo_sobre
                            });
                            showMessage('success', 'Sobre generado exitosamente. ID: ' + data.sobre_id);
                            loadData();
                        }

                        // Desbloquear el botón después de completar el proceso
                        setTimeout(function() {
                            sobreGenerationLocked = false;
                            $("#generateEnvelopeBtn").prop('disabled', false).css('opacity', '1');
                            processingInProgress = false;
                        }, 3000); // Esperar 3 segundos antes de permitir una nueva solicitud
                    },
                    error: function(xhr, status, error) {
                        $("#loader").hide();

                        // Intentar analizar la respuesta JSON para obtener detalles
                        let errorDetails = {
                            status: status,
                            error: error,
                            statusCode: xhr.status,
                            statusText: xhr.statusText
                        };

                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorDetails.response = response;
                            addLogEntry('error', 'Error en la solicitud AJAX', errorDetails);
                        } catch (e) {
                            errorDetails.responseText = xhr.responseText;
                            errorDetails.parsingError = e.message;
                            addLogEntry('error', 'Error en la solicitud AJAX', errorDetails);
                        }

                        // Crear un mensaje de error más descriptivo
                        let errorMessage = 'Error al procesar la solicitud';

                        if (xhr.status) {
                            errorMessage += ` (${xhr.status}: ${xhr.statusText})`;
                        } else {
                            errorMessage += `: ${error}`;
                        }

                        // Mostrar mensaje de error con detalles completos
                        showMessage('error', errorMessage, errorDetails);

                        // Desbloquear el botón después de un error
                        setTimeout(function() {
                            sobreGenerationLocked = false;
                            $("#generateEnvelopeBtn").prop('disabled', false).css('opacity', '1');
                            processingInProgress = false;
                        }, 3000); // Esperar 3 segundos antes de permitir una nueva solicitud
                    }
                });
            }
        }

        let processingInProgress = false;

        // Variable para controlar el estado del botón de procesamiento
        let processBatchLocked = false;

        function processPendingDTEs(tipoDTE) {
            // Verificar si ya hay un proceso en curso
            if (processingInProgress) {
                showMessage('warning', 'Ya hay un proceso en curso. Por favor espere.');
                return;
            }

            // Verificar si el botón está bloqueado (prevención de doble clic)
            if (processBatchLocked) {
                showMessage('warning', 'Solicitud en proceso. Por favor espere unos segundos antes de intentar nuevamente.');
                return;
            }

            // Bloquear el botón para evitar múltiples solicitudes
            processBatchLocked = true;
            $("#processPendingBtn").prop('disabled', true).css('opacity', '0.6');

            // Iniciar el proceso
            processingInProgress = true;
            $("#loader").show();
            $("#responseMessage").hide();

            // Limpiar logs anteriores
            clearLogs();

            // Mostrar el contenedor de logs (asegurarse de que esté visible)
            $("#logContainer").show();
            $("#detailedLogsContainer").show();

            // Agregar un log inicial con información sobre el tipo de DTE
            let tipoTextoLog = tipoDTE === 'all' ? 'todos los tipos' : `tipo ${tipoDTE}`;
            addLogEntry('info', `Iniciando procesamiento de documentos pendientes (${tipoTextoLog})`, {
                tipo_dte: tipoDTE,
                timestamp: new Date().toISOString()
            });

            // Generar un token único para esta solicitud
            const requestToken = Date.now().toString() + Math.random().toString(36).substring(2, 15);

            $.ajax({
                url: tipoDTE === 'all'
                    ? `procesar_sobres_pendientes.php?request_token=${requestToken}&limite=50&maxSobres=1`
                    : `procesar_sobres_pendientes.php?tipo_dte=${tipoDTE}&request_token=${requestToken}&limite=50&maxSobres=1`,
                type: 'GET',
                dataType: 'json'
            })
            .always(function() {
                // Desbloquear el botón después de un tiempo
                setTimeout(function() {
                    processBatchLocked = false;
                    $("#processPendingBtn").prop('disabled', false).css('opacity', '1');
                    processingInProgress = false;
                    $("#loader").hide();
                }, 3000); // Esperar 3 segundos antes de permitir una nueva solicitud
            })
            .done(function(data) {
                if (data.error) {
                    addLogEntry('error', 'Error en el procesamiento', data.details || data.error);

                    // Mostrar mensaje de error con detalles si existen
                    if (data.details) {
                        showMessage('error', data.error, data.details);
                    } else if (data.response_details) {
                        showMessage('error', data.error, data.response_details);
                    } else {
                        showMessage('error', data.error);
                    }
                    return;
                }

                // Registrar resultados detallados en el log
                const tipoLog = data.errores > 0 ? 'warning' : 'success';
                const mensajeLog = data.errores > 0
                    ? `Procesamiento completado con ${data.errores} errores`
                    : `Procesamiento completado: ${data.sobres_generados} sobres generados`;

                addLogEntry(tipoLog, mensajeLog, {
                    documentos_procesados: data.procesados,
                    errores: data.errores,
                    sobres_generados: data.sobres_generados
                });

                // Si hay detalle por tipo, mostrarlo
                if (data.detalle_por_tipo && data.detalle_por_tipo.length > 0) {
                    data.detalle_por_tipo.forEach(detalle => {
                        // Determinar el tipo de log basado en si hubo errores
                        const tipoLogDetalle = detalle.errores > 0 ? 'warning' : 'info';

                        addLogEntry(tipoLogDetalle, `Resumen Tipo DTE ${detalle.tipo_dte}`, {
                            total_inicial: detalle.total_inicial,
                            procesados: detalle.procesados,
                            pendientes: detalle.pendientes_restantes,
                            sobres: detalle.sobres_generados,
                            errores: detalle.errores
                        });

                        // Si hay errores, mostrar un mensaje específico
                        if (detalle.errores > 0) {
                            addLogEntry('error', `Se encontraron ${detalle.errores} errores al procesar documentos de tipo ${detalle.tipo_dte}`, {
                                tipo_dte: detalle.tipo_dte,
                                total_errores: detalle.errores
                            });
                        }
                    });
                }

                // Si hay resultados detallados, mostrarlos
                if (data.resultados && data.resultados.length > 0) {
                    data.resultados.forEach((resultado, idx) => {
                        if (!resultado.exito) {
                            addLogEntry('error', `Error en intento #${idx+1} para tipo DTE ${resultado.tipo_dte}`, {
                                error: resultado.error,
                                detalle_error: resultado.detalle_error || 'No hay detalles adicionales',
                                diagnostico: resultado.diagnostico || 'No hay diagnóstico disponible',
                                http_code: resultado.http_code
                            });

                            // Si hay logs específicos, mostrarlos
                            if (resultado.logs && resultado.logs.length > 0) {
                                resultado.logs.forEach(log => {
                                    if (log.type === 'error') {
                                        addLogEntry('error', `Log del proceso: ${log.message}`, log.data);
                                    }
                                });
                            }
                        }
                    });
                }

                // Mostrar mensaje según si hubo errores o no
                const tipoMensaje = data.errores > 0 ? 'warning' : 'success';
                const mensaje = data.mensaje_detallado ||
                    (data.errores > 0
                        ? `Procesamiento completado con ${data.errores} errores. Revise los logs para más detalles.`
                        : `Procesamiento completado. ${data.procesados} documentos procesados en ${data.sobres_generados} sobres.`);

                showMessage(tipoMensaje, mensaje);

                // Recargar datos
                loadData();
            })
            .fail(function(xhr, status, error) {
                // Crear un objeto con detalles del error
                let errorDetails = {
                    status: status,
                    error: error,
                    statusCode: xhr.status,
                    statusText: xhr.statusText
                };

                try {
                    // Intentar analizar la respuesta como JSON
                    if (xhr.responseText) {
                        errorDetails.response = JSON.parse(xhr.responseText);
                    }
                } catch (e) {
                    // Si no es JSON, guardar el texto plano
                    errorDetails.responseText = xhr.responseText;
                }

                // Registrar el error con todos los detalles
                addLogEntry('error', 'Error de conexión con el servidor', errorDetails);

                // Crear un mensaje de error descriptivo
                let errorMessage = 'Error de conexión con el servidor';
                if (xhr.status) {
                    errorMessage += ` (${xhr.status}: ${xhr.statusText})`;
                } else if (error) {
                    errorMessage += `: ${error}`;
                }

                // Mostrar mensaje de error con todos los detalles
                showMessage('error', errorMessage, errorDetails);
            });
        }

        function viewEnvelope(id) {
            // Implementar visualización detallada del sobre
            alert('Ver detalles del sobre ID: ' + id);
        }

        function downloadFile(filePath) {
            // Implementar descarga del archivo
            window.open('download.php?file=' + encodeURIComponent(filePath), '_blank');
        }

        function showMessage(type, message, details = null) {
            const messageDiv = $("#responseMessage");
            messageDiv.removeClass('success-message error-message warning-message');

            if (type === 'success') {
                messageDiv.addClass('success-message').css({
                    'background-color': '#e8f5e9',
                    'color': '#1b5e20',
                    'border': '1px solid #66bb6a'
                });
            } else if (type === 'warning') {
                messageDiv.addClass('warning-message').css({
                    'background-color': '#fff3e0',
                    'color': '#e65100',
                    'border': '1px solid #ffb74d'
                });
            } else {
                messageDiv.addClass('error-message').css({
                    'background-color': '#ffebee',
                    'color': '#b71c1c',
                    'border': '1px solid #ef5350'
                });
            }

            let messageContent = `<i class="fas fa-${type === 'success' ? 'check-circle' : (type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle')}"></i> ${message}`;

            // Verificar si hay un mensaje detallado en los detalles
            let mensajeDetallado = null;
            if (details && typeof details === 'object') {
                if (details.mensaje_detallado) {
                    mensajeDetallado = details.mensaje_detallado;
                } else if (details.detalle_error) {
                    mensajeDetallado = details.detalle_error;
                }
            }

            // Si hay un mensaje detallado, mostrarlo directamente
            if (mensajeDetallado) {
                messageContent += `
                    <div style="margin-top: 10px; padding: 10px; background-color: rgba(0,0,0,0.05); border-radius: 5px; overflow: auto; max-height: 200px;">
                        <pre style="margin: 0; white-space: pre-wrap; font-family: monospace; font-size: 14px;">${mensajeDetallado}</pre>
                    </div>
                `;
            }

            // Si hay detalles adicionales, agregar un botón para mostrarlos
            if (details) {
                messageContent += `
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleErrorDetails()">
                            <i class="fas fa-info-circle"></i> ${mensajeDetallado ? 'Ver detalles técnicos' : 'Ver detalles'}
                        </button>
                        <div id="errorDetails" style="display: none; margin-top: 10px; padding: 10px; background-color: rgba(0,0,0,0.05); border-radius: 5px; overflow: auto; max-height: 200px;">
                            <pre style="margin: 0; white-space: pre-wrap;">${typeof details === 'object' ? JSON.stringify(details, null, 2) : details}</pre>
                        </div>
                    </div>
                `;
            }

            messageDiv.html(messageContent);
            messageDiv.show();

            // Si es un error o advertencia, asegurarse de que el contenedor de logs esté visible
            if (type === 'error' || type === 'warning') {
                $("#detailedLogsContainer").show();
            }
        }

        // Función para mostrar/ocultar detalles del error
        function toggleErrorDetails() {
            $("#errorDetails").toggle();
        }

        function displayLogs(logs) {
            const logContent = $("#logContent");
            logs.forEach(log => {
                logContent.append(formatLogEntry(log));
            });
            logContent.scrollTop(logContent[0].scrollHeight);
        }

        function formatLogEntry(log) {
            const typeColors = {
                'info': '#2196F3',
                'success': '#4CAF50',
                'error': '#F44336',
                'warning': '#FF9800',
                'debug': '#9C27B0'
            };

            const color = typeColors[log.type] || '#757575';
            let dataHtml = '';

            if (log.data) {
                // Manejo mejorado de datos para errores
                if (log.type === 'error' && typeof log.data === 'object') {
                    // Formato especial para errores con detalles estructurados
                    dataHtml = formatErrorDetails(log.data);
                } else if (typeof log.data === 'object') {
                    // Formato normal para otros objetos
                    dataHtml = '<pre class="log-details" style="margin-left: 20px; font-size: 0.9em; color: #666; max-height: 200px; overflow: auto;">' +
                              JSON.stringify(log.data, null, 2) + '</pre>';
                } else {
                    dataHtml = '<pre class="log-details" style="margin-left: 20px;">' + log.data + '</pre>';
                }
            }

            return `
                <div class="log-entry" style="margin: 8px 0; padding: 8px; border-left: 4px solid ${color}; background: rgba(0,0,0,0.05);">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span style="color: #666; font-size: 0.85em;">[${log.timestamp}]</span>
                        <span style="color: ${color}; font-weight: bold; margin-right: 10px; background-color: ${log.type === 'error' ? 'rgba(244,67,54,0.1)' : 'transparent'}; padding: ${log.type === 'error' ? '2px 8px' : '0'}; border-radius: ${log.type === 'error' ? '12px' : '0'};">${log.type.toUpperCase()}</span>
                    </div>
                    <div style="margin: 5px 0; font-weight: ${log.type === 'error' ? 'bold' : 'normal'};">${log.message}</div>
                    ${dataHtml}
                </div>
            `;
        }

        function clearLogs() {
            $("#logContent").empty();
        }

        // Agregar estas funciones para manejar los logs
        let autoScroll = true;

        function addLogEntry(type, message, details = null) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();

            // Registrar en consola del navegador con más detalles
            console.log(`[${type.toUpperCase()}] ${message}`);

            // Si hay detalles, registrarlos también
            if (details) {
                if (type === 'error') {
                    console.error('Detalles del error:', details);

                    // Registrar información específica sobre errores de cantidad
                    if (details.tipos_encontrados) {
                        console.error('Tipos de DTE encontrados:', details.tipos_encontrados);
                        console.error('ADVERTENCIA: Los sobres deben contener documentos del mismo tipo de DTE');
                    }

                    // Registrar información sobre archivos faltantes
                    if (details.faltantes) {
                        console.error('Archivos XML faltantes:', details.faltantes);
                    }

                    // Registrar diagnóstico detallado si está disponible
                    if (details.diagnostico) {
                        console.error('Diagnóstico detallado:', details.diagnostico);
                    }
                } else {
                    console.log('Detalles:', details);
                }
            }

            const entry = formatLogEntry({
                type: type,
                timestamp: timestamp,
                message: message,
                data: details
            });

            logContent.innerHTML += entry;

            if (autoScroll) {
                logContent.scrollTop = logContent.scrollHeight;
            }
        }

        function clearLogs() {
            document.getElementById('logContent').innerHTML = '';
        }

        document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);
        document.getElementById('toggleAutoScrollBtn').addEventListener('click', function() {
            autoScroll = !autoScroll;
            this.innerHTML = autoScroll ?
                '<i class="fas fa-scroll"></i> Auto-scroll ON' :
                '<i class="fas fa-scroll"></i> Auto-scroll OFF';
        });

        function loadPendingDocumentsCount() {
            $.ajax({
                url: 'api_sobres.php?action=pending_count',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.pending !== undefined) {
                        $("#pendingCount").text(data.pending);

                        // Opcional: Mostrar/ocultar botones según haya o no documentos pendientes
                        if (data.pending > 0) {
                            $("#generateEnvelopeBtn").show();
                            $("#processPendingBtn").show();
                        } else {
                            $("#generateEnvelopeBtn").hide();
                            $("#processPendingBtn").hide();
                        }
                    }
                },
                error: function() {
                    $("#pendingCount").text("Error");
                }
            });
        }

        // Función para formatear categorías de error
        function formatErrorCategory(category) {
            const categories = {
                'file_not_found': 'Archivo no encontrado',
                'permission_denied': 'Permiso denegado',
                'memory_limit': 'Límite de memoria',
                'timeout': 'Tiempo de espera agotado',
                'database_error': 'Error de base de datos',
                'api_error': 'Error de API',
                'xml_error': 'Error en XML',
                'multiple_dte_types': 'Múltiples tipos de DTE'
            };

            return categories[category] || category;
        }

        // Función para solicitar folios con un modal personalizado
        function solicitarFolios() {
            // Limpiar cualquier modal existente
            limpiarModalesExistentes();

            // Limpiar logs anteriores y mostrar el contenedor de logs
            clearLogs();
            document.getElementById('detailedLogsContainer').style.display = 'block';

            // Registrar inicio del proceso en los logs
            addLogEntry('info', 'Iniciando proceso de solicitud de folios CAF');

            // Crear el contenedor del modal personalizado
            const modalContainer = document.createElement('div');
            modalContainer.id = 'customModalContainer';
            modalContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;

            // Crear el contenido del modal
            modalContainer.innerHTML = `
                <div id="customModalContent" style="
                    background-color: white;
                    border-radius: 8px;
                    width: 500px;
                    max-width: 90%;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    overflow: hidden;
                ">
                    <div style="
                        background-color: #2980b9;
                        color: white;
                        padding: 15px 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <h5 style="margin: 0; font-size: 18px;">
                            <i class="fas fa-file-invoice"></i> Solicitar Folios CAF
                        </h5>
                        <button id="customModalClose" style="
                            background: none;
                            border: none;
                            color: white;
                            font-size: 20px;
                            cursor: pointer;
                        ">
                            &times;
                        </button>
                    </div>

                    <div style="padding: 20px;">
                        <form id="foliosForm">
                            <div style="margin-bottom: 15px;">
                                <label for="tipoDTE" style="display: block; margin-bottom: 5px; font-weight: 500;">Tipo de Documento</label>
                                <select id="tipoDTE" name="tipoDTE" required style="
                                    width: 100%;
                                    padding: 8px 12px;
                                    border: 1px solid #ced4da;
                                    border-radius: 4px;
                                    font-size: 16px;
                                ">
                                    <option value="33" selected>Factura Electrónica (33)</option>
                                    <option value="39">Boleta Electrónica (39)</option>
                                    <option value="61">Nota de Crédito (61)</option>
                                    <option value="56">Nota de Débito (56)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label for="cantidad" style="display: block; margin-bottom: 5px; font-weight: 500;">Cantidad de Folios</label>
                                <input type="number" id="cantidad" name="cantidad" value="19" min="1" max="100" required style="
                                    width: 100%;
                                    padding: 8px 12px;
                                    border: 1px solid #ced4da;
                                    border-radius: 4px;
                                    font-size: 16px;
                                ">
                                <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                                    Número de folios a solicitar (máximo 100)
                                </div>
                            </div>
                        </form>

                        <div id="foliosResponseMessage" style="
                            padding: 10px 15px;
                            border-radius: 4px;
                            margin-top: 15px;
                            display: none;
                        "></div>
                    </div>

                    <div style="
                        padding: 15px 20px;
                        background-color: #f8f9fa;
                        border-top: 1px solid #dee2e6;
                        display: flex;
                        justify-content: flex-end;
                    ">
                        <button id="customModalCancel" style="
                            background-color: #6c757d;
                            color: white;
                            border: none;
                            padding: 8px 15px;
                            border-radius: 4px;
                            margin-right: 10px;
                            cursor: pointer;
                        ">Cancelar</button>

                        <button id="confirmarSolicitarFolios" style="
                            background-color: #2980b9;
                            color: white;
                            border: none;
                            padding: 8px 15px;
                            border-radius: 4px;
                            cursor: pointer;
                        "><i class="fas fa-paper-plane"></i> Solicitar Folios</button>
                    </div>
                </div>
            `;

            // Agregar el modal al DOM
            document.body.appendChild(modalContainer);
            addLogEntry('debug', 'Modal de solicitud de folios creado');

            // Configurar eventos para cerrar el modal
            document.getElementById('customModalClose').addEventListener('click', cerrarModal);
            document.getElementById('customModalCancel').addEventListener('click', cerrarModal);

            // Cerrar el modal si se hace clic fuera del contenido
            modalContainer.addEventListener('click', function(event) {
                if (event.target === modalContainer) {
                    cerrarModal();
                }
            });

            // Configurar evento para el botón de confirmación
            document.getElementById('confirmarSolicitarFolios').addEventListener('click', function() {
                // Obtener valores del formulario
                const tipoDTE = document.getElementById('tipoDTE').value;
                const cantidad = document.getElementById('cantidad').value;

                // Validar formulario
                if (!tipoDTE || !cantidad) {
                    mostrarMensajeFolios('error', 'Por favor complete todos los campos');
                    addLogEntry('error', 'Validación de formulario fallida', 'Campos incompletos');
                    return;
                }

                // Registrar en logs los datos de la solicitud
                addLogEntry('info', 'Enviando solicitud de folios', {
                    tipoDTE: tipoDTE,
                    cantidad: cantidad,
                    timestamp: new Date().toISOString()
                });

                // Deshabilitar botón para evitar múltiples envíos
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Procesando...';
                addLogEntry('debug', 'Botón de solicitud deshabilitado para prevenir envíos múltiples');

                // Limpiar mensaje anterior
                const msgElement = document.getElementById('foliosResponseMessage');
                msgElement.style.display = 'none';

                // Enviar solicitud
                addLogEntry('info', `Enviando solicitud al servidor: Tipo DTE ${tipoDTE}, Cantidad ${cantidad}`);
                $.ajax({
                    url: 'solicitar_folios.php',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        tipoDTE: tipoDTE,
                        cantidad: cantidad
                    },
                    success: function(response) {
                        if (response.error) {
                            addLogEntry('error', 'Error en la solicitud de folios', response.error);
                            mostrarMensajeFolios('error', response.error);
                        } else {
                            addLogEntry('success', 'Folios solicitados correctamente', {
                                rango_inicial: response.rango_inicial,
                                rango_final: response.rango_final,
                                total_folios: response.rango_final - response.rango_inicial + 1,
                                archivo_xml: response.archivo_xml || 'No disponible'
                            });

                            // Si hay logs adicionales en la respuesta, mostrarlos
                            if (response.logs && Array.isArray(response.logs)) {
                                response.logs.forEach(log => {
                                    addLogEntry(log.type || 'info', log.message, log.data);
                                });
                            }

                            mostrarMensajeFolios('success', `Folios solicitados correctamente. Rango: ${response.rango_inicial} - ${response.rango_final}`);

                            // Recargar datos después de 2 segundos
                            addLogEntry('info', 'Recargando datos en 2 segundos...');
                            setTimeout(function() {
                                cerrarModal();
                                loadData();
                            }, 2000);
                        }
                    },
                    error: function(xhr, status, error) {
                        const errorMsg = 'Error de conexión con el servidor';
                        addLogEntry('error', errorMsg, {
                            status: status,
                            error: error,
                            response: xhr.responseText || 'Sin respuesta'
                        });
                        mostrarMensajeFolios('error', errorMsg);
                    },
                    complete: function() {
                        // Restaurar botón
                        const btn = document.getElementById('confirmarSolicitarFolios');
                        if (btn) {
                            btn.disabled = false;
                            btn.innerHTML = '<i class="fas fa-paper-plane"></i> Solicitar Folios';
                            addLogEntry('debug', 'Botón de solicitud restaurado');
                        }
                    }
                });
            });

            // Función para cerrar el modal
            function cerrarModal() {
                addLogEntry('info', 'Cerrando modal de solicitud de folios');
                const modalContainer = document.getElementById('customModalContainer');
                if (modalContainer) {
                    modalContainer.remove();
                }
            }

            // Función para mostrar mensajes en el modal
            function mostrarMensajeFolios(tipo, mensaje) {
                const msgElement = document.getElementById('foliosResponseMessage');
                if (msgElement) {
                    msgElement.style.backgroundColor = tipo === 'error' ? '#f8d7da' : '#d4edda';
                    msgElement.style.color = tipo === 'error' ? '#721c24' : '#155724';
                    msgElement.style.border = `1px solid ${tipo === 'error' ? '#f5c6cb' : '#c3e6cb'}`;
                    msgElement.textContent = mensaje;
                    msgElement.style.display = 'block';
                }
            }
        }

        // Función para limpiar cualquier modal existente
        function limpiarModalesExistentes() {
            // Eliminar cualquier modal personalizado existente
            const existingModal = document.getElementById('customModalContainer');
            if (existingModal) {
                existingModal.remove();
            }

            // Eliminar cualquier modal de Bootstrap existente
            const bootstrapModals = document.querySelectorAll('.modal');
            bootstrapModals.forEach(modal => modal.remove());

            // Eliminar cualquier backdrop modal que pueda haber quedado
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            // Restaurar el estado del body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    </script>


        <script>
            // Función para enviar sobre DTE
document.addEventListener('DOMContentLoaded', function() {
    const enviarSobreBtn = document.getElementById('enviarSobreBtn');

    if (enviarSobreBtn) {
        enviarSobreBtn.addEventListener('click', function() {
            // Crear y mostrar un indicador de carga simple
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'enviarSobreLoadingIndicator';
            loadingIndicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;

            const loadingContent = document.createElement('div');
            loadingContent.style.cssText = `
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
            `;
            loadingContent.innerHTML = `
                <div>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-spinner fa-spin fa-3x"></i>
                    </div>
                    <p>Enviando sobres pendientes al SII...</p>
                </div>
            `;

            loadingIndicator.appendChild(loadingContent);
            document.body.appendChild(loadingIndicator);

            // Realizar la petición AJAX al endpoint enviar_sobre.php
            fetch('enviar_sobre.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Eliminar indicador de carga
                document.body.removeChild(loadingIndicator);

                if (data.success) {
                    // Si la operación fue exitosa
                    alert(`¡Éxito! ${data.mensaje} ${data.trackid ? 'TrackID: ' + data.trackid : ''}`);
                } else {
                    // Si hubo un error
                    alert(`Error: ${data.error || 'Ocurrió un error al enviar el sobre'}`);
                }
            })
            .catch(error => {
                console.error('Error en la petición:', error);

                // Eliminar indicador de carga
                document.body.removeChild(loadingIndicator);

                // Mostrar mensaje de error
                alert(`Error de conexión al enviar el sobre: ${error.message}`);
            });
        });
    }
});

            // Función para ejecutar la herramienta de diagnóstico
        function runDiagnosticTool() {
            // Mostrar indicador de carga
            $("#loader").show();

            // Limpiar logs anteriores
            clearLogs();

            // Mostrar el contenedor de logs
            $("#detailedLogsContainer").show();
            addLogEntry('info', 'Iniciando herramienta de diagnóstico');

            // Realizar la petición AJAX al script de diagnóstico
            $.ajax({
                url: 'check_xml_files.php',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    $("#loader").hide();

                    if (data.success) {
                        // Registrar información del sistema
                        addLogEntry('info', 'Información del sistema', data.system_info);

                        // Registrar estado de directorios
                        Object.keys(data.directorios).forEach(function(dir) {
                            const dirInfo = data.directorios[dir];
                            const dirExists = dirInfo.existe === 'Sí';

                            if (dirExists) {
                                addLogEntry('success', `Directorio: ${dir}`, dirInfo);
                            } else {
                                addLogEntry('error', `Directorio no encontrado: ${dir}`, dirInfo);
                            }
                        });

                        // Registrar estado de archivos XML
                        addLogEntry('info', `Documentos pendientes: ${data.documentos_pendientes_total}`);

                        let xmlValidos = 0;
                        let xmlNoEncontrados = 0;

                        data.xml_documentos.forEach(function(xml) {
                            if (xml.existe === 'Sí') {
                                if (xml.es_xml_valido) {
                                    xmlValidos++;
                                    addLogEntry('success', `XML válido: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                                } else {
                                    addLogEntry('warning', `XML con errores: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                                }
                            } else {
                                xmlNoEncontrados++;
                                addLogEntry('error', `XML no encontrado: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                            }
                        });

                        // Mostrar resumen
                        let statusType = 'info';
                        let statusMessage = '';

                        if (xmlNoEncontrados > 0) {
                            statusType = 'error';
                            statusMessage = `Diagnóstico completado: ${xmlNoEncontrados} archivos XML no encontrados. Revise las rutas y permisos.`;

                            // Sugerir posibles soluciones
                            const directorioPadre = data.xml_documentos[0]?.directorio_padre || 'N/A';
                            addLogEntry('info', 'Posibles soluciones:', [
                                `1. Verifique que el directorio '${directorioPadre}' existe y tiene permisos adecuados.`,
                                '2. Compruebe que las rutas en la base de datos corresponden a las ubicaciones reales de los archivos.',
                                '3. Asegúrese de que el usuario del servidor web tiene permisos de lectura en los directorios correspondientes.'
                            ]);
                        } else if (xmlValidos < data.documentos_pendientes_total) {
                            statusType = 'warning';
                            statusMessage = `Diagnóstico completado: Algunos archivos XML (${data.documentos_pendientes_total - xmlValidos}) tienen errores de formato.`;
                        } else {
                            statusType = 'success';
                            statusMessage = `Diagnóstico completado: Todos los archivos XML (${xmlValidos}) son válidos.`;
                        }

                        // Mostrar resultado general
                        showMessage(statusType, statusMessage);

                    } else {
                        // Error en la ejecución del diagnóstico
                        addLogEntry('error', 'Error en la herramienta de diagnóstico', {
                            error: data.error,
                            file: data.error_file,
                            line: data.error_line
                        });

                        showMessage('error', 'Error al ejecutar el diagnóstico: ' + data.error);
                    }
                },
                error: function(xhr, status, error) {
                    $("#loader").hide();

                    addLogEntry('error', 'Error de conexión con el servidor', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });

                    showMessage('error', 'Error al conectar con el servidor: ' + error);
                }
            });
        }

        </script>

</body>
</html>
