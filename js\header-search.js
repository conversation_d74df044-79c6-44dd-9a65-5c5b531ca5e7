// Funcionalidad de búsqueda para el header
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando búsqueda del header...');

    // Obtener referencias a los elementos
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-icon');
    const resetButton = document.getElementById('reset-filters');

    console.log('Elementos de búsqueda:', {
        input: searchInput ? 'Encontrado' : 'No encontrado',
        searchBtn: searchButton ? 'Encontrado' : 'No encontrado',
        resetBtn: resetButton ? 'Encontrado' : 'No encontrado'
    });

    if (searchInput && searchButton && resetButton) {
        // Función para realizar la búsqueda
        function performSearch() {
            console.log('Realizando búsqueda...');
            const searchTerm = searchInput.value.toLowerCase().trim();
            console.log('Término de búsqueda:', searchTerm);

            if (!searchTerm) {
                // Si no hay término de búsqueda, mostrar todos los productos
                resetSearch();
                return;
            }

            // Forzar un repintado completo antes de aplicar los filtros
            // Esto ayuda a evitar problemas con estilos que no se aplican correctamente
            document.body.style.opacity = '0.99';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 5);

            // Guardar el término de búsqueda en una variable global para que otros scripts puedan acceder a él
            window.currentSearchTerm = searchTerm;

            // Obtener el estado actual de la vista (tarjetas o tabla)
            // Usar la variable global establecida por view-switcher.js
            const activeView = window.currentView || 'table';
            console.log('Vista activa:', activeView);

            // Filtrar ambas vistas (tarjetas y tabla) independientemente de cuál esté visible
            // Esto permite que al cambiar de vista, los filtros se mantengan

            // Buscar en las tarjetas
            const cards = document.querySelectorAll('.product-card');
            let foundInCards = false;
            console.log('Tarjetas encontradas:', cards.length);

            if (cards.length > 0) {
                cards.forEach((card, index) => {
                    const sku = card.querySelector('.product-sku')?.textContent.toLowerCase().trim() || '';
                    const name = card.querySelector('.product-name')?.textContent.toLowerCase().trim() || '';
                    const brand = card.querySelector('.product-brand')?.textContent.toLowerCase().trim() || '';
                    const model = card.querySelector('.product-model')?.textContent.toLowerCase().trim() || '';

                    console.log(`Tarjeta ${index + 1} - SKU: "${sku}", Nombre: "${name}", Marca: "${brand}", Modelo: "${model}"`);

                    if (sku.includes(searchTerm) || name.includes(searchTerm) ||
                        brand.includes(searchTerm) || model.includes(searchTerm)) {
                        // Marcar la tarjeta como coincidente con el filtro
                        card.setAttribute('data-matches-filter', 'true');
                        // Mostrar solo si estamos en vista de tarjetas
                        card.style.display = activeView === 'grid' ? 'flex' : 'none';
                        foundInCards = true;
                        console.log(`Tarjeta ${index + 1} coincide con "${searchTerm}". Mostrar: ${activeView === 'grid'}`);
                    } else {
                        // Marcar la tarjeta como no coincidente
                        card.setAttribute('data-matches-filter', 'false');
                        // Ocultar en ambas vistas
                        card.style.display = 'none';
                        console.log(`Tarjeta ${index + 1} NO coincide con "${searchTerm}". Ocultar.`);
                    }
                });
            }

            // Buscar en la tabla - SOLUCIÓN DIRECTA
            const tableRows = document.querySelectorAll('.products-table tbody tr');
            let foundInTable = false;
            console.log('Filas de tabla encontradas:', tableRows.length);

            if (tableRows.length > 0) {
                console.log('APLICANDO FILTRO DIRECTO A LA TABLA');

                // Recorremos todas las filas para buscar coincidencias
                tableRows.forEach((row, index) => {
                    const cells = row.querySelectorAll('td');
                    let matchFound = false;

                    // Buscar en cada celda
                    Array.from(cells).forEach((cell, cellIndex) => {
                        const cellText = cell.textContent.toLowerCase().trim();
                        if (cellText.includes(searchTerm)) {
                            matchFound = true;
                            matchingText = cellText;
                            matchingCellIndex = cellIndex;
                            console.log(`Coincidencia en fila ${index + 1}, celda ${cellIndex + 1}: "${cellText}"`);
                        }
                    });

                    // APLICAR VISIBILIDAD DIRECTAMENTE
                    if (matchFound) {
                        // Si hay coincidencia, mostrar la fila
                        row.style.cssText = 'display: table-row !important';
                        console.log(`MOSTRANDO Fila ${index + 1}: Coincide con "${searchTerm}"`);
                        foundInTable = true;
                    } else {
                        // Si no hay coincidencia, ocultar la fila
                        row.style.cssText = 'display: none !important';
                        console.log(`OCULTANDO Fila ${index + 1}: No coincide con "${searchTerm}"`);
                    }

                    // Guardar el estado en el atributo data-matches-filter
                    row.setAttribute('data-matches-filter', matchFound ? 'true' : 'false');
                });

                // Verificar si se encontraron resultados
                console.log(`Resultados encontrados en tabla: ${foundInTable}`);

                // Forzar repintado de la tabla
                const tableContainer = document.querySelector('.products-table');
                if (tableContainer) {
                    console.log('Forzando repintado de la tabla');
                    // Truco para forzar repintado
                    tableContainer.style.opacity = '0.99';
                    setTimeout(() => {
                        tableContainer.style.opacity = '1';
                    }, 10);
                }
            }

            // Mostrar mensaje si no se encontraron resultados en la vista activa
            const noResultsInCurrentView =
                (activeView === 'grid' && !foundInCards && cards.length > 0) ||
                (activeView === 'table' && !foundInTable && tableRows.length > 0);

            if (noResultsInCurrentView) {
                showNoResultsMessage();
            } else {
                hideNoResultsMessage();
            }

            // Registrar en consola el estado final
            console.log('Resultados de búsqueda:', {
                termino: searchTerm,
                vistaActiva: activeView,
                resultadosEnTarjetas: foundInCards,
                resultadosEnTabla: foundInTable
            });
        }

        // Función para resetear la búsqueda
        function resetSearch() {
            console.log('Reseteando búsqueda...');
            // Limpiar el input de búsqueda
            searchInput.value = '';

            // Limpiar la variable global de búsqueda
            window.currentSearchTerm = '';
            console.log('Variable global de búsqueda limpiada');

            // Obtener la vista activa actual usando la variable global
            const activeView = window.currentView || 'table';

            // Mostrar todas las tarjetas
            const cards = document.querySelectorAll('.product-card');
            console.log(`Reseteando ${cards.length} tarjetas`);
            cards.forEach((card, index) => {
                // Limpiar el atributo de filtro y cualquier estilo inline
                card.removeAttribute('data-matches-filter');
                card.removeAttribute('style');
                console.log(`Tarjeta ${index + 1}: Reseteo completo`);
            });

            // Mostrar todas las filas de la tabla
            const tableRows = document.querySelectorAll('.products-table tbody tr');
            console.log(`Reseteando ${tableRows.length} filas de tabla`);
            tableRows.forEach((row, index) => {
                // Limpiar el atributo de filtro y cualquier estilo inline
                row.removeAttribute('data-matches-filter');
                row.removeAttribute('style');
                console.log(`Fila ${index + 1}: Reseteo completo`);
            });

            console.log('Vista activa al resetear:', activeView);

            // Ocultar mensaje de no resultados
            hideNoResultsMessage();

            // Forzar actualización de la vista actual
            // Verificar si la función global está disponible
            if (typeof window.setActiveView === 'function') {
                window.setActiveView(activeView);
            } else {
                console.log('La función setActiveView no está disponible, actualizando manualmente');
                // Actualizar manualmente la vista
                if (activeView === 'grid') {
                    document.body.classList.remove('table-view');
                    document.body.classList.add('grid-view');
                } else {
                    document.body.classList.remove('grid-view');
                    document.body.classList.add('table-view');
                }
            }

            console.log('Búsqueda reseteada, vista actual:', activeView);
        }

        // Función para mostrar mensaje de no resultados
        function showNoResultsMessage() {
            console.log('Mostrando mensaje de no resultados');
            // Verificar si ya existe el mensaje
            let noResultsMsg = document.getElementById('no-results-message');

            if (!noResultsMsg) {
                // Crear el mensaje si no existe
                noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'no-results-message';
                noResultsMsg.className = 'no-results';
                noResultsMsg.innerHTML = `
                    <div style="text-align: center; padding: 20px; margin: 20px auto; max-width: 500px; background-color: #f8f9fa; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <i class="fas fa-search" style="font-size: 3rem; color: #95a5a6; margin-bottom: 15px;"></i>
                        <h3 style="color: #2c3e50; margin-bottom: 10px;">No se encontraron resultados</h3>
                        <p style="color: #7f8c8d;">No hay productos que coincidan con tu búsqueda. Intenta con otros términos o restablece los filtros.</p>
                    </div>
                `;

                // Insertar después del contenedor de productos
                const productsContainer = document.getElementById('products-container');
                console.log('Contenedor de productos:', productsContainer ? 'Encontrado' : 'No encontrado');

                if (productsContainer) {
                    // Insertar después del contenedor de productos
                    productsContainer.insertAdjacentElement('afterend', noResultsMsg);
                } else {
                    // Si no se encuentra el contenedor, insertar en el body
                    document.body.appendChild(noResultsMsg);
                }
            } else {
                // Mostrar el mensaje si ya existe
                noResultsMsg.style.display = 'block';
            }
        }

        // Función para ocultar mensaje de no resultados
        function hideNoResultsMessage() {
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.style.display = 'none';
            }
        }

        // Función para forzar la actualización de la tabla
        function forceTableUpdate() {
            console.log('Forzando actualización de la tabla...');

            // Obtener todas las filas de la tabla
            const tableRows = document.querySelectorAll('.products-table tbody tr');

            // Aplicar estilos directamente a cada fila según su atributo data-matches-filter
            tableRows.forEach(row => {
                if (row.getAttribute('data-matches-filter') === 'true') {
                    row.style.cssText = 'display: table-row !important';
                } else if (row.getAttribute('data-matches-filter') === 'false') {
                    row.style.cssText = 'display: none !important';
                }
            });

            // Forzar repintado de la tabla
            const tableContainer = document.querySelector('.products-table');
            if (tableContainer) {
                tableContainer.style.opacity = '0.99';
                setTimeout(() => {
                    tableContainer.style.opacity = '1';
                }, 10);
            }
        }

        // Event listeners
        searchButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Botón de búsqueda clickeado');
            performSearch();

            // Forzar actualización de la tabla después de un breve retraso
            setTimeout(forceTableUpdate, 50);
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                console.log('Tecla Enter presionada en el input de búsqueda');
                performSearch();

                // Forzar actualización de la tabla después de un breve retraso
                setTimeout(forceTableUpdate, 50);
            }
        });

        resetButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Botón de reset clickeado');
            resetSearch();

            // Forzar actualización de la tabla después de un breve retraso
            setTimeout(forceTableUpdate, 50);
        });

        // Enfocar el input de búsqueda cuando se carga la página
        searchInput.focus();

        // Respetar la configuración de vista establecida por view-switcher.js
        console.log('Respetando la configuración de vista establecida por view-switcher.js');

        console.log('Inicialización completa de la funcionalidad de búsqueda del header');
    }
});

