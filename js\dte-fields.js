// Función para alternar los campos según el tipo de DTE seleccionado
function toggleDTEFields() {
    console.log('toggleDTEFields ejecutándose');
    const tipoDTE = document.getElementById('tipoDTE').value;
    console.log('Tipo DTE seleccionado:', tipoDTE);

    const facturaFields = document.querySelectorAll('.factura-field');
    const boletaFields = document.querySelectorAll('.boleta-field');
    const referenciaSection = document.getElementById('referenciasSection');
    const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');
    const defaultFacturaCheckbox = document.getElementById('defaultFacturaCheckbox');
    const precioTipoLabel = document.getElementById('precio-tipo-label');
    const precioInfo = document.getElementById('precio-info');

    console.log('defaultReceptorCheckbox encontrado:', !!defaultReceptorCheckbox);

    // Por defecto, ocultar campos de referencia
    if (referenciaSection) {
        referenciaSection.style.display = 'none';
    }

    // Mostrar/ocultar campos según el tipo de DTE
    if (tipoDTE === '39') { // Boleta
        console.log('Procesando tipo Boleta');
        facturaFields.forEach(field => field.style.display = 'none');
        boletaFields.forEach(field => field.style.display = 'block');
        if (precioTipoLabel) precioTipoLabel.textContent = "(Total)";
        if (precioInfo) precioInfo.textContent = "En boletas, el precio es total (incluye IVA)";

        // Para boletas, siempre activar el receptor genérico automáticamente
        if (defaultReceptorCheckbox) {
            console.log('Activando receptor genérico para boleta');
            defaultReceptorCheckbox.checked = true;
            // Llamar a la función para completar los campos
            toggleDefaultReceptor();
        } else {
            console.log('No se encontró defaultReceptorCheckbox');
        }

        // Ocultar el checkbox de factura por defecto si está visible
        if (document.querySelector('.factura-default-container')) {
            document.querySelector('.factura-default-container').style.display = 'none';
        }
    } else if (tipoDTE === '61') { // Nota de Crédito
        facturaFields.forEach(field => field.style.display = 'block');
        boletaFields.forEach(field => field.style.display = 'none');
        if (referenciaSection) {
            referenciaSection.style.display = 'block';
        }
        precioTipoLabel.textContent = "(Neto)";
        precioInfo.textContent = "En notas de crédito, el precio es neto (sin IVA)";

        // Para notas de crédito, desactivar el receptor genérico de boletas si está activo
        if (defaultReceptorCheckbox && defaultReceptorCheckbox.checked) {
            defaultReceptorCheckbox.checked = false;
            if (typeof toggleDefaultReceptor === 'function') {
                toggleDefaultReceptor();
            }
        }

        // Ocultar el checkbox de factura por defecto si está visible
        if (document.querySelector('.factura-default-container')) {
            document.querySelector('.factura-default-container').style.display = 'none';
        }
    } else { // Facturas (33 o 34)
        facturaFields.forEach(field => field.style.display = 'block');
        boletaFields.forEach(field => field.style.display = 'none');
        precioTipoLabel.textContent = "(Neto)";
        precioInfo.textContent = "En facturas, el precio es neto (sin IVA)";

        // Para facturas, desactivar el receptor genérico de boletas si está activo
        if (defaultReceptorCheckbox && defaultReceptorCheckbox.checked) {
            defaultReceptorCheckbox.checked = false;
            if (typeof toggleDefaultReceptor === 'function') {
                toggleDefaultReceptor();
            }
        }

        // Mostrar el checkbox de factura por defecto
        if (document.querySelector('.factura-default-container')) {
            document.querySelector('.factura-default-container').style.display = 'block';
        }
    }

    // Actualizar cálculos
    if (typeof calcularTotales === 'function') {
        calcularTotales();
    }
}

// Función para alternar el receptor predeterminado para boletas
function toggleDefaultReceptor() {
    console.log('toggleDefaultReceptor ejecutándose');
    const checkbox = document.getElementById('defaultReceptorCheckbox');
    const rutInput = document.getElementById('rutReceptor');
    const razonSocialInput = document.getElementById('razonSocialReceptor');
    const direccionInput = document.getElementById('direccionReceptor');
    const comunaInput = document.getElementById('comunaReceptor');
    const contactoInput = document.getElementById('contactoReceptor');

    console.log('Checkbox estado:', checkbox ? checkbox.checked : 'no encontrado');
    console.log('Campos encontrados:', {
        rut: !!rutInput,
        razonSocial: !!razonSocialInput,
        direccion: !!direccionInput,
        comuna: !!comunaInput,
        contacto: !!contactoInput
    });

    if (checkbox && checkbox.checked) {
        console.log('Completando campos con receptor genérico');
        // Usar receptor genérico para boletas
        if (rutInput) rutInput.value = "66666666-6";
        if (razonSocialInput) razonSocialInput.value = "Cliente Boleta";
        if (direccionInput) direccionInput.value = "Temuco";
        if (comunaInput) comunaInput.value = "Temuco";
        if (contactoInput) contactoInput.value = "";

        // Deshabilitar campos para evitar modificaciones
        if (rutInput) {
            rutInput.readOnly = true;
            rutInput.classList.add('readonly-field');
        }
        if (razonSocialInput) {
            razonSocialInput.readOnly = true;
            razonSocialInput.classList.add('readonly-field');
        }
        if (direccionInput) {
            direccionInput.readOnly = true;
            direccionInput.classList.add('readonly-field');
        }
        if (comunaInput) {
            comunaInput.readOnly = true;
            comunaInput.classList.add('readonly-field');
        }
    } else {
        // Habilitar campos para edición manual
        if (rutInput) {
            rutInput.readOnly = false;
            rutInput.classList.remove('readonly-field');
        }
        if (razonSocialInput) {
            razonSocialInput.readOnly = false;
            razonSocialInput.classList.remove('readonly-field');
        }
        if (direccionInput) {
            direccionInput.readOnly = false;
            direccionInput.classList.remove('readonly-field');
        }
        if (comunaInput) {
            comunaInput.readOnly = false;
            comunaInput.classList.remove('readonly-field');
        }

        // Limpiar campos cuando se deshabilita el receptor genérico
        if (rutInput) rutInput.value = "";
        if (razonSocialInput) razonSocialInput.value = "";
        if (direccionInput) direccionInput.value = "";
        if (comunaInput) comunaInput.value = "";
        if (contactoInput) contactoInput.value = "";
    }
}

// Función para alternar el receptor predeterminado para facturas
function toggleDefaultFactura() {
    const checkbox = document.getElementById('defaultFacturaCheckbox');
    const rutInput = document.getElementById('rutReceptor');
    const razonSocialInput = document.getElementById('razonSocialReceptor');
    const direccionInput = document.getElementById('direccionReceptor');
    const comunaInput = document.getElementById('comunaReceptor');
    const giroInput = document.getElementById('giroReceptor');

    if (checkbox && checkbox.checked) {
        // Usar receptor por defecto para facturas
        if (rutInput) rutInput.value = "76594370-8";
        if (razonSocialInput) razonSocialInput.value = "Auto Paris SPA";
        if (direccionInput) direccionInput.value = "Los Claveles 2950";
        if (comunaInput) comunaInput.value = "Temuco";
        if (giroInput) giroInput.value = "Venta de vehículos y repuestos";
        
        // Deshabilitar campos
        if (rutInput) rutInput.readOnly = true;
        if (razonSocialInput) razonSocialInput.readOnly = true;
        if (direccionInput) direccionInput.readOnly = true;
        if (comunaInput) comunaInput.readOnly = true;
        if (giroInput) giroInput.readOnly = true;
    } else {
        // Habilitar campos
        if (rutInput) rutInput.readOnly = false;
        if (razonSocialInput) razonSocialInput.readOnly = false;
        if (direccionInput) direccionInput.readOnly = false;
        if (comunaInput) comunaInput.readOnly = false;
        if (giroInput) giroInput.readOnly = false;
        
        // Limpiar campos
        if (rutInput) rutInput.value = "";
        if (razonSocialInput) razonSocialInput.value = "";
        if (direccionInput) direccionInput.value = "";
        if (comunaInput) comunaInput.value = "";
        if (giroInput) giroInput.value = "";
    }
}

// Inicializar campos al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    // Esperar un momento para asegurarnos de que todos los elementos estén disponibles
    setTimeout(function() {
        // Agregar el checkbox para facturas si no existe
        const receptorSection = document.querySelector('.dte-form-section:has(h3:contains("Receptor"))');
        if (receptorSection) {
            // Verificar si ya existe el checkbox para facturas
            if (!document.getElementById('defaultFacturaCheckbox')) {
                // Crear el contenedor para el checkbox de factura
                const facturaDefaultContainer = document.createElement('div');
                facturaDefaultContainer.className = 'form-group factura-field factura-default-container';
                facturaDefaultContainer.style.marginBottom = '15px';
                facturaDefaultContainer.innerHTML = `
                    <label class="checkbox-container">
                        <input type="checkbox" id="defaultFacturaCheckbox">
                        <span class="checkbox-label">Usar receptor por defecto (76594370-8 - Auto Paris)</span>
                    </label>
                `;
                
                // Insertar después del checkbox de boleta
                const boletaContainer = document.querySelector('.boleta-field');
                if (boletaContainer && boletaContainer.parentNode) {
                    boletaContainer.parentNode.insertBefore(facturaDefaultContainer, boletaContainer.nextSibling);
                } else {
                    // Si no hay checkbox de boleta, insertar al inicio de la sección
                    const firstFormRow = receptorSection.querySelector('.form-row');
                    if (firstFormRow) {
                        receptorSection.insertBefore(facturaDefaultContainer, firstFormRow);
                    }
                }
                
                // Agregar evento al checkbox
                const defaultFacturaCheckbox = document.getElementById('defaultFacturaCheckbox');
                if (defaultFacturaCheckbox) {
                    defaultFacturaCheckbox.addEventListener('change', toggleDefaultFactura);
                }
            }
        }
        
        // Configurar tipo DTE y campos asociados
        toggleDTEFields();
        
        // Inicializar checkbox de receptor por defecto para boletas
        const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');
        console.log('Inicializando defaultReceptorCheckbox:', !!defaultReceptorCheckbox);

        if (defaultReceptorCheckbox) {
            // Remover cualquier event listener previo para evitar duplicados
            defaultReceptorCheckbox.removeEventListener('change', toggleDefaultReceptor);
            defaultReceptorCheckbox.addEventListener('change', toggleDefaultReceptor);

            // Si el tipo DTE actual es boleta, activar automáticamente el receptor genérico
            const tipoDTE = document.getElementById('tipoDTE').value;
            console.log('Tipo DTE en inicialización:', tipoDTE);

            if (tipoDTE === '39') {
                console.log('Activando receptor genérico en inicialización');
                defaultReceptorCheckbox.checked = true;
                toggleDefaultReceptor();
            }
        } else {
            console.log('defaultReceptorCheckbox no encontrado en inicialización');
        }
        
        // Actualizar el folio para boleta
        if (typeof fetchFolio === 'function') {
            fetchFolio();
        }
    }, 300);
});

// Función adicional para inicializar cuando se abre el canvas DTE
function initializeDTECanvas() {
    console.log('Inicializando canvas DTE');

    // Esperar un momento para que el DOM esté completamente cargado
    setTimeout(() => {
        const tipoDTE = document.getElementById('tipoDTE');
        const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');

        console.log('Canvas - tipoDTE:', tipoDTE ? tipoDTE.value : 'no encontrado');
        console.log('Canvas - defaultReceptorCheckbox:', !!defaultReceptorCheckbox);

        if (tipoDTE && defaultReceptorCheckbox) {
            // Si es boleta, activar automáticamente
            if (tipoDTE.value === '39') {
                console.log('Canvas - Activando receptor genérico para boleta');
                defaultReceptorCheckbox.checked = true;
                toggleDefaultReceptor();
            }
        }
    }, 100);
}

// Hacer la función disponible globalmente
window.initializeDTECanvas = initializeDTECanvas;

// Función adicional que se ejecuta cada vez que se abre el canvas
function forceBoletaReceptorGenerico() {
    console.log('Forzando receptor genérico para boleta');

    const tipoDTE = document.getElementById('tipoDTE');
    const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');

    if (tipoDTE && defaultReceptorCheckbox && tipoDTE.value === '39') {
        console.log('Aplicando receptor genérico forzado');
        defaultReceptorCheckbox.checked = true;

        // Completar campos directamente
        const rutInput = document.getElementById('rutReceptor');
        const razonSocialInput = document.getElementById('razonSocialReceptor');
        const direccionInput = document.getElementById('direccionReceptor');
        const comunaInput = document.getElementById('comunaReceptor');
        const contactoInput = document.getElementById('contactoReceptor');

        if (rutInput) {
            rutInput.value = "66666666-6";
            rutInput.readOnly = true;
            rutInput.classList.add('readonly-field');
        }
        if (razonSocialInput) {
            razonSocialInput.value = "Cliente Boleta";
            razonSocialInput.readOnly = true;
            razonSocialInput.classList.add('readonly-field');
        }
        if (direccionInput) {
            direccionInput.value = "Temuco";
            direccionInput.readOnly = true;
            direccionInput.classList.add('readonly-field');
        }
        if (comunaInput) {
            comunaInput.value = "Temuco";
            comunaInput.readOnly = true;
            comunaInput.classList.add('readonly-field');
        }
        if (contactoInput) {
            contactoInput.value = "";
        }

        console.log('Receptor genérico aplicado exitosamente');
    }
}

// Hacer disponible globalmente
window.forceBoletaReceptorGenerico = forceBoletaReceptorGenerico;