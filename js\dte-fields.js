// Función para alternar los campos según el tipo de DTE seleccionado
function toggleDTEFields() {
    const tipoDTE = document.getElementById('tipoDTE').value;
    const facturaFields = document.querySelectorAll('.factura-field');
    const boletaFields = document.querySelectorAll('.boleta-field');
    const referenciaSection = document.getElementById('referenciasSection');
    const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');
    const defaultFacturaCheckbox = document.getElementById('defaultFacturaCheckbox');
    const precioTipoLabel = document.getElementById('precio-tipo-label');
    const precioInfo = document.getElementById('precio-info');

    // Por defecto, ocultar campos de referencia
    if (referenciaSection) {
        referenciaSection.style.display = 'none';
    }

    // Mostrar/ocultar campos según el tipo de DTE
    if (tipoDTE === '39') { // Boleta
        facturaFields.forEach(field => field.style.display = 'none');
        boletaFields.forEach(field => field.style.display = 'block');
        precioTipoLabel.textContent = "(Total)";
        precioInfo.textContent = "En boletas, el precio es total (incluye IVA)";

        // Si es la primera vez que se selecciona boleta, marcar la casilla por defecto
        if (defaultReceptorCheckbox && !defaultReceptorCheckbox.dataset.initialized) {
            defaultReceptorCheckbox.checked = true;
            defaultReceptorCheckbox.dataset.initialized = "true";
            if (typeof toggleDefaultReceptor === 'function') {
                toggleDefaultReceptor();
            }
        }
        
        // Ocultar el checkbox de factura por defecto si está visible
        if (document.querySelector('.factura-default-container')) {
            document.querySelector('.factura-default-container').style.display = 'none';
        }
    } else if (tipoDTE === '61') { // Nota de Crédito
        facturaFields.forEach(field => field.style.display = 'block');
        boletaFields.forEach(field => field.style.display = 'none');
        if (referenciaSection) {
            referenciaSection.style.display = 'block';
        }
        precioTipoLabel.textContent = "(Neto)";
        precioInfo.textContent = "En notas de crédito, el precio es neto (sin IVA)";
        
        // Ocultar el checkbox de factura por defecto si está visible
        if (document.querySelector('.factura-default-container')) {
            document.querySelector('.factura-default-container').style.display = 'none';
        }
    } else { // Facturas (33 o 34)
        facturaFields.forEach(field => field.style.display = 'block');
        boletaFields.forEach(field => field.style.display = 'none');
        precioTipoLabel.textContent = "(Neto)";
        precioInfo.textContent = "En facturas, el precio es neto (sin IVA)";
        
        // Mostrar el checkbox de factura por defecto
        if (document.querySelector('.factura-default-container')) {
            document.querySelector('.factura-default-container').style.display = 'block';
        }
    }

    // Actualizar cálculos
    if (typeof calcularTotales === 'function') {
        calcularTotales();
    }
}

// Función para alternar el receptor predeterminado para boletas
function toggleDefaultReceptor() {
    const checkbox = document.getElementById('defaultReceptorCheckbox');
    const rutInput = document.getElementById('rutReceptor');
    const razonSocialInput = document.getElementById('razonSocialReceptor');
    const direccionInput = document.getElementById('direccionReceptor');
    const comunaInput = document.getElementById('comunaReceptor');

    if (checkbox && checkbox.checked) {
        // Usar receptor genérico
        if (rutInput) rutInput.value = "66666666-6";
        if (razonSocialInput) razonSocialInput.value = "Cliente Boleta";
        if (direccionInput) direccionInput.value = "Ciudad";
        if (comunaInput) comunaInput.value = "Comuna";
        
        // Deshabilitar campos
        if (rutInput) rutInput.readOnly = true;
        if (razonSocialInput) razonSocialInput.readOnly = true;
        if (direccionInput) direccionInput.readOnly = true;
        if (comunaInput) comunaInput.readOnly = true;
    } else {
        // Habilitar campos
        if (rutInput) rutInput.readOnly = false;
        if (razonSocialInput) razonSocialInput.readOnly = false;
        if (direccionInput) direccionInput.readOnly = false;
        if (comunaInput) comunaInput.readOnly = false;
        
        // Limpiar campos
        if (rutInput) rutInput.value = "";
        if (razonSocialInput) razonSocialInput.value = "";
        if (direccionInput) direccionInput.value = "";
        if (comunaInput) comunaInput.value = "";
    }
}

// Función para alternar el receptor predeterminado para facturas
function toggleDefaultFactura() {
    const checkbox = document.getElementById('defaultFacturaCheckbox');
    const rutInput = document.getElementById('rutReceptor');
    const razonSocialInput = document.getElementById('razonSocialReceptor');
    const direccionInput = document.getElementById('direccionReceptor');
    const comunaInput = document.getElementById('comunaReceptor');
    const giroInput = document.getElementById('giroReceptor');

    if (checkbox && checkbox.checked) {
        // Usar receptor por defecto para facturas
        if (rutInput) rutInput.value = "76594370-8";
        if (razonSocialInput) razonSocialInput.value = "Auto Paris SPA";
        if (direccionInput) direccionInput.value = "Los Claveles 2950";
        if (comunaInput) comunaInput.value = "Temuco";
        if (giroInput) giroInput.value = "Venta de vehículos y repuestos";
        
        // Deshabilitar campos
        if (rutInput) rutInput.readOnly = true;
        if (razonSocialInput) razonSocialInput.readOnly = true;
        if (direccionInput) direccionInput.readOnly = true;
        if (comunaInput) comunaInput.readOnly = true;
        if (giroInput) giroInput.readOnly = true;
    } else {
        // Habilitar campos
        if (rutInput) rutInput.readOnly = false;
        if (razonSocialInput) razonSocialInput.readOnly = false;
        if (direccionInput) direccionInput.readOnly = false;
        if (comunaInput) comunaInput.readOnly = false;
        if (giroInput) giroInput.readOnly = false;
        
        // Limpiar campos
        if (rutInput) rutInput.value = "";
        if (razonSocialInput) razonSocialInput.value = "";
        if (direccionInput) direccionInput.value = "";
        if (comunaInput) comunaInput.value = "";
        if (giroInput) giroInput.value = "";
    }
}

// Inicializar campos al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    // Esperar un momento para asegurarnos de que todos los elementos estén disponibles
    setTimeout(function() {
        // Agregar el checkbox para facturas si no existe
        const receptorSection = document.querySelector('.dte-form-section:has(h3:contains("Receptor"))');
        if (receptorSection) {
            // Verificar si ya existe el checkbox para facturas
            if (!document.getElementById('defaultFacturaCheckbox')) {
                // Crear el contenedor para el checkbox de factura
                const facturaDefaultContainer = document.createElement('div');
                facturaDefaultContainer.className = 'form-group factura-field factura-default-container';
                facturaDefaultContainer.style.marginBottom = '15px';
                facturaDefaultContainer.innerHTML = `
                    <label class="checkbox-container">
                        <input type="checkbox" id="defaultFacturaCheckbox">
                        <span class="checkbox-label">Usar receptor por defecto (76594370-8 - Auto Paris)</span>
                    </label>
                `;
                
                // Insertar después del checkbox de boleta
                const boletaContainer = document.querySelector('.boleta-field');
                if (boletaContainer && boletaContainer.parentNode) {
                    boletaContainer.parentNode.insertBefore(facturaDefaultContainer, boletaContainer.nextSibling);
                } else {
                    // Si no hay checkbox de boleta, insertar al inicio de la sección
                    const firstFormRow = receptorSection.querySelector('.form-row');
                    if (firstFormRow) {
                        receptorSection.insertBefore(facturaDefaultContainer, firstFormRow);
                    }
                }
                
                // Agregar evento al checkbox
                const defaultFacturaCheckbox = document.getElementById('defaultFacturaCheckbox');
                if (defaultFacturaCheckbox) {
                    defaultFacturaCheckbox.addEventListener('change', toggleDefaultFactura);
                }
            }
        }
        
        // Configurar tipo DTE y campos asociados
        toggleDTEFields();
        
        // Inicializar checkbox de receptor por defecto para boletas
        const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');
        if (defaultReceptorCheckbox) {
            defaultReceptorCheckbox.addEventListener('change', toggleDefaultReceptor);
            // Marcar como inicializado para la primera carga
            defaultReceptorCheckbox.checked = true;
            defaultReceptorCheckbox.dataset.initialized = "true";
            toggleDefaultReceptor();
        }
        
        // Actualizar el folio para boleta
        if (typeof fetchFolio === 'function') {
            fetchFolio();
        }
    }, 300);
});